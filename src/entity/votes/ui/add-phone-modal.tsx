"use client"

import React, {useEffect} from "react"
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/shared/ui/dialog"
import { <PERSON><PERSON> } from "@/shared/ui/button"
import { Label } from "@/shared/ui/label"
import { Loader2 } from "lucide-react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { addPhoneSchema, AddPhoneFormValues } from "../model/add-phone-schema"
import { useAddPhone } from "../model/use-add-phone"
import { VillagePeopleSelect } from "@/entity/village-people/ui/select"
import {ModalControlType} from "@/shared/lib/use-modal-control";
import {IMaskInput} from "react-imask";
import {useCurrentUser} from "@/entity/users/hooks/use-current-user";
import { extractLast6Digits } from "../lib/phone-utils";

export type AddPhoneModalParams = {
    voteId: string;
    ownerId?: string
    phoneNumber?: string
    originalPhoneNumber: string
}

type AddPhoneModalProps = {
    onSuccess?: () => void;
    modalControl: ModalControlType<AddPhoneModalParams>
}

export function AddPhoneModal({ onSuccess, modalControl }: AddPhoneModalProps) {

    const defaultOwnerId = modalControl.modalProps.ownerId;
    const defaultPhoneNumber = modalControl.modalProps.phoneNumber;
    const originalPhoneNumber = modalControl.modalProps.originalPhoneNumber;
    const currentUser = useCurrentUser()

    // Используем react-hook-form с zod для валидации
    const {
        register,
        handleSubmit,
        setValue,
        watch,
        reset,
        formState: { errors, isSubmitting }
    } = useForm<AddPhoneFormValues>({
        resolver: zodResolver(addPhoneSchema),
        defaultValues: {
            voteId: modalControl.modalProps.voteId,
            phoneNumber: "",
            ownerId: modalControl.modalProps.ownerId || undefined
        }
    })

    // Используем кастомный хук для добавления номера телефона
    const addPhoneMutation = useAddPhone()

    // Получаем текущее значение ownerId
    const ownerId = watch("ownerId")

    // Сбрасываем форму при закрытии модального окна
    useEffect(() => {
        if (!modalControl.visible) {
            reset({
                phoneNumber: "",
                ownerId: defaultOwnerId || undefined
            })
        }
    }, [modalControl.visible, reset, defaultOwnerId, defaultPhoneNumber])

    useEffect(() => {
        setValue('voteId', modalControl.modalProps.voteId, { shouldValidate: true })
    }, [modalControl.modalProps.voteId, setValue]);

    // Обработчик изменения значения в селекте
    const handleOwnerChange = (value: string) => {
        setValue("ownerId", value, { shouldValidate: true })
    }

    // Обработчик отправки формы
    const onSubmit = async (data: AddPhoneFormValues) => {
        const phone = "+998" + data.phoneNumber + extractLast6Digits(originalPhoneNumber)
        const formData = {
            phoneNumber: defaultPhoneNumber || phone,
            ownerId: defaultOwnerId || data.ownerId,
            voteId: data.voteId,
            updatedBy: currentUser._id
        }
        const result = await addPhoneMutation.mutateAsync(formData)

        if (result) {
            modalControl.closeModal()
            onSuccess?.()
        }
    }

    return (
            <>
                <form onSubmit={handleSubmit(onSubmit)}>
                    <DialogHeader>
                        <DialogTitle>{"Nomer qo'shish"}</DialogTitle>
                    </DialogHeader>

                    <div className="space-y-4 my-6">
                        <div className="space-y-2">
                            <Label htmlFor="phoneNumber">
                                Telefon raqami <span className="text-red-500">*</span>
                            </Label>
                            <div className="flex border rounded-lg overflow-hidden items-center px-2">
                                <span className="w-[100px] h-[42px] border-r flex items-center">+998</span>
                                {/*<Input placeholder="+998" disabled className="w-[100px] h-[42px] border-none" />*/}
                                <IMaskInput
                                  mask="000"
                                  unmask={true}
                                  placeholder="___"
                                  className="border-none p-2 w-[50px] outline-none"
                                  value={watch("phoneNumber")}
                                  {...register("phoneNumber", { required: "Введите телефон" })}
                                  onAccept={(value) => setValue("phoneNumber", value, { shouldValidate: true })}
                                  disabled={isSubmitting || !!defaultPhoneNumber}
                                />
                                <span className="w-[100px] h-[42px] flex items-center">{extractLast6Digits(originalPhoneNumber)}</span>
                            </div>
                            {errors.phoneNumber && (
                                <p className="text-sm text-red-500">
                                    {errors.phoneNumber.message}
                                </p>
                            )}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="ownerId">
                                {"Xo'jalik"}
                            </Label>
                            <VillagePeopleSelect
                                value={ownerId}
                                onValueChange={handleOwnerChange}
                                placeholder="Xo'jalikni tanlang"
                                width="w-full"
                                disabled={!!defaultOwnerId}
                            />
                        </div>
                    </div>

                    <DialogFooter>
                        <Button
                            type="button"
                            variant="outline"
                            onClick={() => modalControl.closeModal()}
                            disabled={isSubmitting}
                        >
                            Bekor qilish
                        </Button>
                        <Button
                            type="submit"
                            disabled={isSubmitting}
                        >
                            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                            Saqlash
                        </Button>
                    </DialogFooter>
                </form>
            </>
    )
}
