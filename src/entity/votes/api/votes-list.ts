'use server';

// Тип для возвращаемых данных
import {dbConnect} from "@/shared/lib/database";
import {VotesSchema} from "@/entity/votes/schema";
import {PaginationList} from "@/shared/api/external";

export type VoteList = {
    _id: string;
    originalPhoneNumber: string;
    voteDate: string;
    phoneNumber?: string; // Номер телефона голосующего
    ownerId?: string; // ID хозяйства, к которому привязан голос
    owner?: { _id: string; name: string }; // Информация о владельце, если ownerId существует
    updatedBy?: string; // Кто обновил данные
    createdAt: string;
    updatedAt: string;
};

// Аргументы функции
export type VotesListParams = {
    page?: number;
    size?: number;
    ownerId?: string; // Фильтрация по ID хозяйства
    phoneNumber?: string; // Фильтрация по номеру телефона
    originalPhoneNumber?: string; // Фильтрация по оригинальному номеру телефона
    excludeWithOwner?: boolean; // Исключить голоса, которые уже привязаны к хозяйству
    onlyWithPhone?: boolean; // Только голоса с номером телефона
    excludeWithPhone?: boolean;
};

export async function getVotesPaginated({
    page = 1,
    size = 20,
    ownerId,
    phoneNumber,
    originalPhoneNumber,
    excludeWithOwner = undefined,
    onlyWithPhone = false
}: VotesListParams): Promise<PaginationList<VoteList>> {
    await dbConnect(); // Подключение к базе

    const skip = (page - 1) * size;

    // Создаем фильтр для поиска
    // eslint-disable-next-line
    const filter: any = {};

    // Если указан ownerId, добавляем его в фильтр
    if (ownerId) {
        filter.ownerId = ownerId;
    }

    // Если указан phoneNumber, добавляем фильтрацию по номеру телефона
    if (phoneNumber) {
        // Экранируем специальные символы регулярных выражений
        const escapedPhoneNumber = phoneNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
        // Фильтруем по номеру телефона (поиск по части номера)
        filter.phoneNumber = { $regex: escapedPhoneNumber, $options: 'i' };
    }

    // Если указан originalPhoneNumber, добавляем фильтрацию по оригинальному номеру телефона
    if (originalPhoneNumber) {
        // Проверяем, если введены только цифры (например, 981616)
        const isOnlyDigits = /^\d+$/.test(originalPhoneNumber);

        if (isOnlyDigits && originalPhoneNumber.length >= 6) {
            // Если введены только цифры, ищем по последним 6 цифрам в формате **-*XX-XX-XX
            const digits = originalPhoneNumber.slice(-6); // Берем последние 6 цифр
            const searchPattern = `.*${digits.slice(0, 2)}-${digits.slice(2, 4)}-${digits.slice(4, 6)}`;
            filter.originalPhoneNumber = { $regex: searchPattern, $options: 'i' };
        } else {
            // Если введен не только цифровой формат, ищем как есть (экранируя спецсимволы)
            const escapedOriginalPhoneNumber = originalPhoneNumber.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
            filter.originalPhoneNumber = { $regex: escapedOriginalPhoneNumber, $options: 'i' };
        }
    }

    // Если нужно исключить голоса, которые уже привязаны к хозяйству
    if (excludeWithOwner) {
        filter.ownerId = { $exists: false };
    }

    if (excludeWithOwner === false) {
        filter.ownerId = { $exists: true };
    }

    // Если нужны только голоса с номером телефона
    if (onlyWithPhone) {
        filter.phoneNumber = { $exists: true, $ne: null };
    }

    // Создаем агрегационный пайплайн для получения данных с информацией о владельце
    const aggregationPipeline = [
        { $match: filter },
        {
            $lookup: {
                from: 'villagePeople',
                localField: 'ownerId',
                foreignField: '_id',
                as: 'ownerInfo'
            }
        },
        { $sort: { voteDate: -1 } },
        {
            $facet: {
                data: [
                    { $skip: skip },
                    { $limit: size || 20 }
                ],
                totalCount: [
                    { $count: "count" }
                ]
            }
        }
    ];

    const [result] = await VotesSchema.aggregate(aggregationPipeline as any);
    const dataRaw = result.data || [];
    const total = result.totalCount[0]?.count || 0;

    // Приводим результат к типу Vote[]
    // eslint-disable-next-line
    // @ts-ignore
    const data = dataRaw.map((doc) => ({
        _id: doc._id?.toString(),
        originalPhoneNumber: doc.originalPhoneNumber,
        voteDate: doc.voteDate || '',
        phoneNumber: doc.phoneNumber || undefined,
        ownerId: doc.ownerId ? doc.ownerId.toString() : undefined,
        owner: doc.ownerInfo && doc.ownerInfo.length > 0 ? {
            _id: doc.ownerInfo[0]._id.toString(),
            name: doc.ownerInfo[0].name
        } : undefined,
        updatedBy: doc.updatedBy || undefined,
        createdAt: doc.createdAt.toISOString(),
        updatedAt: doc.updatedAt.toISOString(),
    })) as VoteList[];

    return {
        data,
        total: total as number,
        totalPages: Math.ceil(total / size),
        currentPage: page as number,
    };
}

export async function getVotesList (searchParams: Promise<VotesListParams>) {
    const params = await searchParams
    return getVotesPaginated(params)
}
