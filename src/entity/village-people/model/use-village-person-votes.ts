"use client"

import { useQuery } from "@tanstack/react-query"
import { getVotesPaginated } from "@/entity/votes/api"
import {toast} from "sonner";

/**
 * Хук для получения голосов, привязанных к хозяйству
 */
export function useVillagePersonVotes(villagePersonId: string) {

  const {
    data,
    isLoading,
    isError,
    error,
    refetch
  } = useQuery({
    queryKey: ["villagePersonVotes", villagePersonId],
    queryFn: async () => {
      try {
        return await getVotesPaginated({
          ownerId: villagePersonId,
          page: 1,
          size: 100,
          excludeWithOwner: false
        })
      } catch (error) {
        console.error("Error fetching village person votes:", error)
        toast("Xatolik", {
          description: "Ovozlarni yuklashda xatolik yuz berdi",
        })
        throw error
      }
    },
    enabled: !!villagePersonId
  })

  return {
    votes: data?.data || [],
    total: data?.total || 0,
    isLoading,
    isError,
    error,
    refetch
  }
}
